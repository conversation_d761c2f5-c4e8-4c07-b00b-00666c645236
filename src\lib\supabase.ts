import { createClient } from '@supabase/supabase-js';

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Define the configuration options
const supabaseOptions = {
  auth: {
    persistSession: true,
    storageKey: 'badhees-auth-token',
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  global: {
    // Disable health checks to prevent 404 errors in console
    fetch: (...args: Parameters<typeof fetch>) => {
      const [url, options] = args;
      // Skip health check requests
      if (typeof url === 'string' && url.includes('health-check')) {
        return Promise.resolve(new Response(JSON.stringify([{ up: true }]), {
          headers: { 'Content-Type': 'application/json' },
          status: 200
        }));
      }
      return fetch(...args);
    }
  }
};

// Check for missing or template environment variables
const isTemplateValue = (value: string | undefined): boolean => {
  return !value ||
         value === 'your_supabase_url_here' ||
         value === 'your_supabase_anon_key_here' ||
         value.includes('your_') ||
         value.includes('_here');
};

if (!supabaseUrl || !supabaseAnonKey || isTemplateValue(supabaseUrl) || isTemplateValue(supabaseAnonKey)) {
  console.error('Missing or template Supabase environment variables - using fallback values that will fail gracefully');

  // Log the actual values (masked for security)
  console.error(`
    Debug Info:
    - VITE_SUPABASE_URL: ${supabaseUrl ? (isTemplateValue(supabaseUrl) ? 'Template value' : `${supabaseUrl.substring(0, 8)}...`) : 'undefined'}
    - VITE_SUPABASE_ANON_KEY: ${supabaseAnonKey ? (isTemplateValue(supabaseAnonKey) ? 'Template value' : 'Set (hidden)') : 'undefined'}
  `);

  // In development, show a more helpful error
  if (import.meta.env.DEV) {
    console.error(`
      ⚠️ Supabase environment variables are missing or using template values!

      Current values in .env file are template values. You need to:
      1. Get your actual Supabase URL and anon key from https://supabase.com/dashboard
      2. Replace the template values in .env file:
         VITE_SUPABASE_URL=https://your-project-ref.supabase.co
         VITE_SUPABASE_ANON_KEY=your-actual-anon-key

      The website will not work properly without real Supabase credentials.
    `);
  }
} else if (import.meta.env.DEV) {
  // In development, confirm that environment variables are loaded correctly
  console.log('✅ Supabase environment variables loaded successfully');
}

// Create and export the Supabase client
// Use safe fallback values to prevent URL constructor errors
const safeSupabaseUrl = (supabaseUrl && !isTemplateValue(supabaseUrl))
  ? supabaseUrl
  : 'https://placeholder.supabase.co';

const safeSupabaseAnonKey = (supabaseAnonKey && !isTemplateValue(supabaseAnonKey))
  ? supabaseAnonKey
  : 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI4MDAsImV4cCI6MTk2MDc2ODgwMH0.placeholder';

export const supabase = createClient(
  safeSupabaseUrl,
  safeSupabaseAnonKey,
  supabaseOptions
);
