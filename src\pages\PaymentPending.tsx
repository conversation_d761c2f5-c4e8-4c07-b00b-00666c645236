import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Loader2, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { createOrder, updateOrderWithPayment } from '@/services/orderService';
import { toast } from '@/hooks/use-toast';

/**
 * Payment Pending Page
 * Polls for payment status when mobile payments are in progress
 */
const PaymentPending: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { clearCart } = useCart();
  
  const [status, setStatus] = useState<'checking' | 'success' | 'failed' | 'timeout'>('checking');
  const [message, setMessage] = useState('Checking payment status...');
  const [countdown, setCountdown] = useState(120); // 2 minutes timeout
  const [orderId, setOrderId] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated || !user) {
      navigate('/login');
      return;
    }

    const razorpayOrderId = searchParams.get('razorpay_order_id');
    const orderIdParam = searchParams.get('order_id');

    if (!razorpayOrderId) {
      setStatus('failed');
      setMessage('Invalid payment session. Please try again.');
      return;
    }

    let pollCount = 0;
    const maxPolls = 24; // 2 minutes with 5-second intervals
    
    const pollPaymentStatus = async () => {
      try {
        console.log(`🔍 Polling payment status (${pollCount + 1}/${maxPolls})`);
        
        const response = await fetch(`${window.location.origin}/api/razorpay/check-payment-status`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            razorpay_order_id: razorpayOrderId,
            order_id: orderIdParam,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          
          if (data.success) {
            if (data.payment_status === 'captured' || data.payment_status === 'authorized') {
              console.log('✅ Payment confirmed!');
              await handlePaymentSuccess(data.payment_id, orderIdParam);
              return;
            } else if (data.payment_status === 'failed') {
              console.log('❌ Payment failed');
              setStatus('failed');
              setMessage('Payment failed. Please try again.');
              return;
            }
          }
        }

        pollCount++;
        if (pollCount >= maxPolls) {
          console.log('⏰ Polling timeout reached');
          setStatus('timeout');
          setMessage('Payment verification timed out. Please check your orders page or contact support if money was deducted.');
          return;
        }

        // Continue polling
        setTimeout(pollPaymentStatus, 5000);
        setCountdown(prev => Math.max(0, prev - 5));
        
      } catch (error) {
        console.error('❌ Error polling payment status:', error);
        pollCount++;
        
        if (pollCount >= maxPolls) {
          setStatus('timeout');
          setMessage('Unable to verify payment status. Please check your orders page or contact support if money was deducted.');
        } else {
          setTimeout(pollPaymentStatus, 5000);
          setCountdown(prev => Math.max(0, prev - 5));
        }
      }
    };

    const handlePaymentSuccess = async (paymentId: string, orderIdParam: string | null) => {
      try {
        // Get stored order data
        const storedOrderData = sessionStorage.getItem('pendingPaymentOrder');
        if (!storedOrderData) {
          throw new Error('Order information not found');
        }

        const orderData = JSON.parse(storedOrderData);

        // Handle order creation for temporary orders
        if (orderData.orderId.startsWith('temp_')) {
          console.log('⏳ Creating order for successful payment...');
          
          const cartData = sessionStorage.getItem('checkoutCart');
          const shippingData = sessionStorage.getItem('checkoutShipping');
          const addressData = sessionStorage.getItem('checkoutAddress');
          
          if (!cartData || !addressData) {
            throw new Error('Order information incomplete');
          }

          const cartItems = JSON.parse(cartData);
          const shippingInfo = shippingData ? JSON.parse(shippingData) : null;
          const addresses = JSON.parse(addressData);

          const subtotal = cartItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
          const shippingFee = shippingInfo?.calculation?.shippingFee || 0;
          const totalAmount = subtotal + shippingFee;

          const newOrder = await createOrder(
            user.id,
            cartItems,
            totalAmount,
            'Online Payment',
            addresses.shipping,
            addresses.billing,
            {
              shippingFee,
              isBangaloreDelivery: shippingInfo?.isBangaloreDelivery,
              shippingNotes: shippingInfo?.calculation?.notes?.join('; ') || '',
            }
          );

          if (!newOrder) {
            throw new Error('Failed to create order');
          }

          setOrderId(newOrder.id);
          await updateOrderWithPayment(newOrder.id, paymentId);
        } else {
          await updateOrderWithPayment(orderData.orderId, paymentId);
          setOrderId(orderData.orderId);
        }

        // Clear cart and stored data
        clearCart(false);
        sessionStorage.removeItem('pendingPaymentOrder');
        sessionStorage.removeItem('checkoutCart');
        sessionStorage.removeItem('checkoutShipping');
        sessionStorage.removeItem('checkoutAddress');

        setStatus('success');
        setMessage('Payment successful! Your order has been placed.');
        
        toast({
          title: 'Payment Successful!',
          description: 'Your order has been placed successfully.',
          variant: 'default'
        });

        setTimeout(() => {
          navigate('/profile/orders');
        }, 3000);

      } catch (error) {
        console.error('❌ Error handling payment success:', error);
        setStatus('failed');
        setMessage('Payment successful but order creation failed. Please contact support.');
      }
    };

    // Start polling
    pollPaymentStatus();

    // Cleanup function
    return () => {
      pollCount = maxPolls; // Stop polling if component unmounts
    };
  }, [searchParams, isAuthenticated, user, navigate, clearCart]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        {status === 'checking' && (
          <>
            <Loader2 className="h-16 w-16 text-blue-600 animate-spin mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Verifying Payment</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="flex items-center justify-center text-sm text-gray-500">
              <Clock className="h-4 w-4 mr-1" />
              <span>Timeout in {countdown} seconds</span>
            </div>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            {orderId && (
              <p className="text-sm text-gray-500">Order ID: {orderId}</p>
            )}
            <p className="text-sm text-gray-500 mt-4">Redirecting to your orders...</p>
          </>
        )}

        {(status === 'failed' || status === 'timeout') && (
          <>
            <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              {status === 'timeout' ? 'Verification Timeout' : 'Payment Failed'}
            </h1>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => navigate('/profile/orders')}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Check Orders
              </button>
              <button
                type="button"
                onClick={() => navigate('/cart')}
                className="w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Return to Cart
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentPending;
