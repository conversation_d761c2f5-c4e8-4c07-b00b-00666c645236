const Razorpay = require('razorpay');

module.exports = async function handler(req, res) {
  // Set CORS headers first
  const origin = req.headers.origin;
  console.log('🌐 Request origin:', origin);

  // Allow requests from any origin that includes thebadhees.com or localhost
  if (origin && (
    origin.includes('thebadhees.com') ||
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('vercel.app')
  )) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token, Origin');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    console.log('✅ Handling OPTIONS preflight request');
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // Validate environment variables
  console.log('🔧 Checking environment variables...');
  console.log('RAZORPAY_KEY_ID exists:', !!process.env.RAZORPAY_KEY_ID);
  console.log('RAZORPAY_SECRET exists:', !!process.env.RAZORPAY_SECRET);
  console.log('RAZORPAY_KEY_ID value:', process.env.RAZORPAY_KEY_ID ? process.env.RAZORPAY_KEY_ID.substring(0, 10) + '...' : 'undefined');
  console.log('All env vars:', Object.keys(process.env).filter(key => key.includes('RAZORPAY')));

  if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_SECRET) {
    console.error('❌ Missing Razorpay environment variables');
    return res.status(500).json({
      success: false,
      error: 'Server configuration error: Missing Razorpay credentials'
    });
  }

  // Initialize Razorpay
  let razorpay;
  try {
    razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_SECRET,
    });
    console.log('✅ Razorpay initialized successfully');
  } catch (initError) {
    console.error('❌ Failed to initialize Razorpay:', initError);
    return res.status(500).json({
      success: false,
      error: 'Failed to initialize payment gateway'
    });
  }



  try {
    console.log('💰 Creating Razorpay order');
    const { amount, currency = 'INR', receipt, notes = {} } = req.body;
    console.log('📋 Order details:', { amount, currency, receipt, notes });

    if (!amount || !receipt) {
      console.error('❌ Missing required parameters:', { amount: !!amount, receipt: !!receipt });
      return res.status(400).json({
        success: false,
        error: 'Amount and receipt are required'
      });
    }

    const options = {
      amount: Math.round(amount * 100), // Convert to paise
      currency,
      receipt,
      notes,
    };

    console.log('🔧 Razorpay options:', options);
    const order = await razorpay.orders.create(options);
    console.log('✅ Razorpay order created successfully:', order.id);

    const response = {
      success: true,
      data: order
    };
    console.log('📤 Sending response:', JSON.stringify(response, null, 2));

    return res.status(200).json(response);
  } catch (error) {
    console.error('❌ Error creating Razorpay order:', error);
    console.error('❌ Error details:', {
      message: error.message,
      code: error.code,
      description: error.description,
      source: error.source,
      step: error.step,
      reason: error.reason
    });

    const errorResponse = {
      success: false,
      error: error.message || 'Failed to create Razorpay order',
      details: error.description || 'Unknown error occurred'
    };
    console.log('📤 Sending error response:', JSON.stringify(errorResponse, null, 2));

    return res.status(500).json(errorResponse);
  }
}
