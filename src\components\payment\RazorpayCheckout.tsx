/**
 * Razorpay Checkout Component
 *
 * This component handles the Razorpay payment flow.
 */
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, CreditCard } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { createRazorpayOrderForOrder, updateOrderWithPayment } from '@/services/orderService';
import { Order } from '@/services/orderService';

// Load Razorpay script
const loadRazorpayScript = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });
};

interface RazorpayCheckoutProps {
  order: Order;
  onSuccess: (paymentId: string) => void;
  onFailure: (error: any) => void;
  buttonText?: string;
  buttonSize?: 'default' | 'sm' | 'lg' | 'icon';
  buttonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  buttonClassName?: string;
  showIcon?: boolean;
}

const RazorpayCheckout: React.FC<RazorpayCheckoutProps> = ({
  order,
  onSuccess,
  onFailure,
  buttonText = 'Pay Now',
  buttonSize = 'default',
  buttonVariant = 'default',
  buttonClassName = '',
  showIcon = true,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const { user } = useAuth();

  // Load Razorpay script on component mount
  useEffect(() => {
    const loadScript = async () => {
      const scriptLoaded = await loadRazorpayScript();
      setIsScriptLoaded(scriptLoaded);
      if (!scriptLoaded) {
        toast.error('Failed to load payment gateway. Please refresh the page and try again.');
      }
    };

    loadScript();
  }, []);

  const handlePayment = async () => {
    if (!isScriptLoaded) {
      toast.error('Payment gateway is not loaded yet. Please wait or refresh the page.');
      return;
    }

    if (!user) {
      toast.error('You need to be logged in to make a payment.');
      return;
    }

    setIsLoading(true);

    try {
      // Create Razorpay order
      const razorpayOrderId = await createRazorpayOrderForOrder(
        order,
        user.email
      );

      if (!razorpayOrderId) {
        throw new Error('Failed to create payment order');
      }

      // Validate environment variables
      if (!import.meta.env.VITE_RAZORPAY_KEY_ID) {
        throw new Error('Missing VITE_RAZORPAY_KEY_ID environment variable');
      }

      // Detect if user is on mobile device
      const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // Get current URL for mobile redirects
      const currentUrl = window.location.origin;
      const successUrl = `${currentUrl}/payment-success?order_id=${encodeURIComponent(order.id)}&razorpay_order_id=${encodeURIComponent(razorpayOrderId)}`;
      const failureUrl = `${currentUrl}/payment-failure?order_id=${encodeURIComponent(order.id)}`;

      // Initialize Razorpay options
      const options: any = {
        key: import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: order.total_amount * 100, // Amount in paise
        currency: 'INR',
        name: 'The Badhees',
        description: `Order #${order.id.substring(0, 8)}`,
        order_id: razorpayOrderId,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email,
          contact: order.shipping_address?.phone || '',
        },
        notes: {
          order_id: order.id,
          shipping_address: JSON.stringify(order.shipping_address),
        },
        theme: {
          color: '#3B82F6', // Blue color
        },
        handler: function (response: any) {
          // Handle successful payment
          handlePaymentSuccess(response);
        },
        modal: {
          ondismiss: function () {
            setIsLoading(false);
            toast.info('Payment cancelled. You can try again later.');
          },
        },
      };

      // Add mobile-specific options for UPI app redirects
      if (isMobile) {
        console.log('📱 Mobile device detected - adding redirect URLs');
        options.callback_url = successUrl;
        options.redirect = true;
        options.modal = {
          ...options.modal,
          confirm_close: false, // Don't show confirmation on mobile
          escape: false, // Disable escape key on mobile
        };

        // Store order details in sessionStorage for mobile redirect handling
        sessionStorage.setItem('pendingPaymentOrder', JSON.stringify({
          orderId: order.id,
          razorpayOrderId: razorpayOrderId,
          amount: order.total_amount,
          timestamp: Date.now()
        }));

        console.log('📱 Mobile payment URLs configured:', { successUrl, failureUrl });
      }

      // Open Razorpay checkout
      const razorpay = new (window as any).Razorpay(options);
      razorpay.open();
    } catch (error) {
      console.error('Error initializing payment:', error);
      setIsLoading(false);
      toast.error('Failed to initialize payment. Please try again.');
      onFailure(error);
    }
  };

  const handlePaymentSuccess = async (response: any) => {
    try {
      // Verify payment on the server
      const baseUrl = import.meta.env.VITE_APP_URL || window.location.origin;
      const verifyUrl = `${baseUrl}/api/razorpay/verify-payment`;

      const res = await fetch(verifyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          razorpay_order_id: response.razorpay_order_id,
          razorpay_payment_id: response.razorpay_payment_id,
          razorpay_signature: response.razorpay_signature,
          order_id: order.id,
        }),
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();

      if (data.success) {
        // Update order with payment details
        await updateOrderWithPayment(order.id, response.razorpay_payment_id);

        // Don't show toast here, it will be shown in the onSuccess callback
        // This prevents duplicate success messages
        onSuccess(response.razorpay_payment_id);
      } else {
        throw new Error(data.error || 'Payment verification failed');
      }
    } catch (error) {
      console.error('Error verifying payment:', error);
      toast.error('Payment verification failed. Please contact support.');
      onFailure(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={isLoading || !isScriptLoaded}
      size={buttonSize}
      variant={buttonVariant}
      className={buttonClassName}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        showIcon && <CreditCard className="h-4 w-4 mr-2" />
      )}
      {isLoading ? 'Processing...' : buttonText}
    </Button>
  );
};

export default RazorpayCheckout;
