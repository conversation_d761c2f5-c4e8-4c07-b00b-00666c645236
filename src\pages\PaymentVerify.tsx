import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, Loader2, AlertCircle, Clock, RefreshCw } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { createOrder } from '@/services/orderService';
import { toast } from '@/hooks/use-toast';


/**
 * Payment Verification Page
 * Handles mobile payment verification with multiple fallback mechanisms
 */
const PaymentVerify: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { clearCart } = useCart();
  
  const [status, setStatus] = useState<'verifying' | 'polling' | 'success' | 'failed'>('verifying');
  const [message, setMessage] = useState('Verifying your payment...');
  const [countdown, setCountdown] = useState(180); // 3 minutes
  const [orderId, setOrderId] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    if (!isAuthenticated || !user) {
      console.log('❌ User not authenticated, redirecting to login');
      navigate('/login');
      return;
    }

    const isDismissed = searchParams.get('dismissed') === 'true';
    console.log('🔍 Payment verification started', { isDismissed });

    startVerificationProcess(isDismissed);
  }, [isAuthenticated, user, navigate, searchParams]);

  const startVerificationProcess = async (isDismissed: boolean = false) => {
    try {
      // Try to get payment data from storage
      const paymentData = null; // Placeholder - will be implemented in new system
      
      if (!paymentData) {
        console.log('❌ No payment data found');
        setStatus('failed');
        setMessage('Payment session not found. Please try again or contact support if money was deducted.');
        return;
      }

      console.log('📦 Found payment data:', paymentData);

      // If we have complete payment response, verify immediately
      if (paymentData.razorpayPaymentId && paymentData.razorpaySignature) {
        console.log('✅ Complete payment data found, verifying...');
        await verifyPaymentWithBackend(paymentData);
        return;
      }

      // If payment was dismissed or we don't have complete data, start polling
      console.log('🔄 Starting payment status polling...');
      setStatus('polling');
      setMessage(isDismissed ? 
        'Checking if payment was completed in UPI app...' : 
        'Waiting for payment completion...'
      );
      
      startPolling(paymentData);

    } catch (error) {
      console.error('❌ Error in verification process:', error);
      setStatus('failed');
      setMessage('Verification failed. Please contact support if money was deducted.');
    }
  };



  const startPolling = (paymentData: any) => {
    let pollCount = 0;
    const maxPolls = 36; // 3 minutes with 5-second intervals

    const poll = async () => {
      try {
        console.log(`🔍 Polling attempt ${pollCount + 1}/${maxPolls}`);

        const response = await fetch('/api/razorpay/check-payment-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            razorpay_order_id: paymentData.razorpayOrderId,
            order_id: paymentData.orderId,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          
          if (data.success && (data.payment_status === 'captured' || data.payment_status === 'authorized')) {
            console.log('✅ Payment confirmed via polling!');
            
            // Update payment data with found payment ID
            const updatedPaymentData = {
              ...paymentData,
              razorpayPaymentId: data.payment_id,
              status: 'captured'
            };

            await handleSuccessfulPayment(updatedPaymentData);
            return;
          } else if (data.payment_status === 'failed') {
            console.log('❌ Payment failed');
            setStatus('failed');
            setMessage('Payment failed. Please try again.');
            return;
          }
        }

        pollCount++;
        setCountdown(prev => Math.max(0, prev - 5));

        if (pollCount >= maxPolls) {
          console.log('⏰ Polling timeout reached');
          setStatus('failed');
          setMessage('Payment verification timed out. Please check your orders or contact support if money was deducted.');
          return;
        }

        // Continue polling
        setTimeout(poll, 5000);

      } catch (error) {
        console.error('❌ Polling error:', error);
        pollCount++;
        
        if (pollCount >= maxPolls) {
          setStatus('failed');
          setMessage('Unable to verify payment. Please check your orders or contact support if money was deducted.');
        } else {
          setTimeout(poll, 5000);
          setCountdown(prev => Math.max(0, prev - 5));
        }
      }
    };

    poll();
  };

  const verifyPaymentWithBackend = async (paymentData: any) => {
    try {
      console.log('🔐 Verifying payment with backend...');

      const response = await fetch('/api/razorpay/verify-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          razorpay_order_id: paymentData.razorpayOrderId,
          razorpay_payment_id: paymentData.razorpayPaymentId,
          razorpay_signature: paymentData.razorpaySignature,
          order_id: paymentData.orderId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Verification failed: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Payment verification failed');
      }

      console.log('✅ Payment verified successfully');
      await handleSuccessfulPayment(paymentData);

    } catch (error) {
      console.error('❌ Backend verification error:', error);
      setStatus('failed');
      setMessage('Payment verification failed. Please contact support if money was deducted.');
    }
  };

  const handleSuccessfulPayment = async (paymentData: any) => {
    try {
      console.log('🎉 Processing successful payment...');

      // Create order if it's a temporary order
      if (paymentData.orderId.startsWith('temp_')) {
        console.log('📦 Creating order for successful payment...');
        
        const orderData = paymentData.orderData;
        if (!orderData) {
          throw new Error('Order data not found');
        }

        const newOrder = await createOrder(
          user.id,
          orderData.items,
          paymentData.amount,
          orderData.payment_method,
          orderData.shipping_address,
          orderData.billing_address,
          {
            shippingFee: orderData.shipping_fee,
            isBangaloreDelivery: orderData.shipping_address?.city?.toLowerCase().includes('bangalore'),
          }
        );

        if (!newOrder) {
          throw new Error('Failed to create order');
        }

        console.log('✅ Order created:', newOrder.id);
        setOrderId(newOrder.id);

        // Payment processing will be handled by new system
        console.log('✅ Order created successfully, payment processing complete');
      } else {
        // For existing orders, just set the order ID
        setOrderId(paymentData.orderId);
        console.log('✅ Existing order payment processed');
      }

      // Clear cart and stored data
      console.log('🛒 Clearing cart and payment data...');
      clearCart(false);
      
      // Clear all payment-related storage
      // Will be implemented in new system

      setStatus('success');
      setMessage('Payment successful! Your order has been placed.');
      
      toast({
        title: 'Payment Successful!',
        description: 'Your order has been placed successfully.',
        variant: 'default'
      });

      // Redirect to orders page
      setTimeout(() => {
        navigate('/profile/orders');
      }, 3000);

    } catch (error) {
      console.error('❌ Error handling successful payment:', error);
      setStatus('failed');
      setMessage('Payment successful but order creation failed. Please contact support.');
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    setStatus('verifying');
    setMessage('Retrying verification...');
    setCountdown(180);
    startVerificationProcess();
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        {(status === 'verifying' || status === 'polling') && (
          <>
            <Loader2 className="h-16 w-16 text-blue-600 animate-spin mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              {status === 'verifying' ? 'Verifying Payment' : 'Checking Payment Status'}
            </h1>
            <p className="text-gray-600 mb-4">{message}</p>
            {status === 'polling' && (
              <div className="flex items-center justify-center text-sm text-gray-500">
                <Clock className="h-4 w-4 mr-1" />
                <span>Timeout in {countdown} seconds</span>
              </div>
            )}
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            {orderId && (
              <p className="text-sm text-gray-500">Order ID: {orderId}</p>
            )}
            <p className="text-sm text-gray-500 mt-4">Redirecting to your orders...</p>
          </>
        )}

        {status === 'failed' && (
          <>
            <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Verification Failed</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="space-y-2">
              {retryCount < 2 && (
                <button
                  type="button"
                  onClick={handleRetry}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Verification
                </button>
              )}
              <button
                type="button"
                onClick={() => navigate('/profile/orders')}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Check My Orders
              </button>
              <button
                type="button"
                onClick={() => navigate('/cart')}
                className="w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Return to Cart
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentVerify;
