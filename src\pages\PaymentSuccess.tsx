import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { createOrder, updateOrderWithPayment } from '@/services/orderService';
import { toast } from '@/hooks/use-toast';

/**
 * Payment Success Page
 * Handles mobile payment redirects from Razorpay UPI apps
 */
const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { clearCart } = useCart();
  
  const [isProcessing, setIsProcessing] = useState(true);
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Processing your payment...');
  const [orderId, setOrderId] = useState<string | null>(null);

  useEffect(() => {
    const processPaymentSuccess = async () => {
      try {
        // Get URL parameters
        const razorpayPaymentId = searchParams.get('razorpay_payment_id');
        const razorpayOrderId = searchParams.get('razorpay_order_id');
        const razorpaySignature = searchParams.get('razorpay_signature');
        const orderIdParam = searchParams.get('order_id');

        console.log('📱 Mobile payment success page loaded');
        console.log('📋 URL parameters:', {
          razorpayPaymentId: !!razorpayPaymentId,
          razorpayOrderId: !!razorpayOrderId,
          razorpaySignature: !!razorpaySignature,
          orderIdParam
        });

        // Check if user is authenticated
        if (!isAuthenticated || !user) {
          console.error('❌ User not authenticated');
          setStatus('error');
          setMessage('Authentication required. Please log in and try again.');
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        // Validate required parameters
        if (!razorpayPaymentId || !razorpayOrderId || !razorpaySignature) {
          console.error('❌ Missing payment parameters');
          setStatus('error');
          setMessage('Invalid payment response. Please contact support if money was deducted.');
          return;
        }

        // Get stored order details from sessionStorage
        const storedOrderData = sessionStorage.getItem('pendingPaymentOrder');
        if (!storedOrderData) {
          console.error('❌ No stored order data found');
          setStatus('error');
          setMessage('Order information not found. Please contact support if money was deducted.');
          return;
        }

        const orderData = JSON.parse(storedOrderData);
        console.log('📦 Retrieved stored order data:', orderData);

        // Verify payment with backend
        console.log('🔐 Verifying payment with backend...');
        const verifyUrl = `${window.location.origin}/api/razorpay/verify-payment`;
        
        const verifyResponse = await fetch(verifyUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            razorpay_order_id: razorpayOrderId,
            razorpay_payment_id: razorpayPaymentId,
            razorpay_signature: razorpaySignature,
            order_id: orderData.orderId,
          }),
        });

        if (!verifyResponse.ok) {
          throw new Error(`Payment verification failed: ${verifyResponse.status}`);
        }

        const verifyData = await verifyResponse.json();
        
        if (!verifyData.success) {
          throw new Error(verifyData.error || 'Payment verification failed');
        }

        console.log('✅ Payment verified successfully');

        // Handle order creation for temporary orders
        if (orderData.orderId.startsWith('temp_')) {
          console.log('⏳ Creating order for temporary payment...');
          
          // Get cart items and shipping info from sessionStorage
          const cartData = sessionStorage.getItem('checkoutCart');
          const shippingData = sessionStorage.getItem('checkoutShipping');
          const addressData = sessionStorage.getItem('checkoutAddress');
          
          if (!cartData || !addressData) {
            throw new Error('Order information incomplete. Please contact support.');
          }

          const cartItems = JSON.parse(cartData);
          const shippingInfo = shippingData ? JSON.parse(shippingData) : null;
          const addresses = JSON.parse(addressData);

          // Calculate total including shipping
          const subtotal = cartItems.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
          const shippingFee = shippingInfo?.calculation?.shippingFee || 0;
          const totalAmount = subtotal + shippingFee;

          // Create the order
          const newOrder = await createOrder(
            user.id,
            cartItems,
            totalAmount,
            'Online Payment',
            addresses.shipping,
            addresses.billing,
            {
              shippingFee,
              isBangaloreDelivery: shippingInfo?.isBangaloreDelivery,
              shippingNotes: shippingInfo?.calculation?.notes?.join('; ') || '',
            }
          );

          if (!newOrder) {
            throw new Error('Failed to create order');
          }

          console.log('📦 Order created successfully:', newOrder.id);
          setOrderId(newOrder.id);

          // Update order with payment details
          await updateOrderWithPayment(newOrder.id, razorpayPaymentId);
          console.log('💳 Order updated with payment details');
        } else {
          // For existing orders, just update with payment details
          await updateOrderWithPayment(orderData.orderId, razorpayPaymentId);
          setOrderId(orderData.orderId);
          console.log('💳 Existing order updated with payment details');
        }

        // Clear cart and stored data
        console.log('🛒 Clearing cart and stored data...');
        clearCart(false);
        sessionStorage.removeItem('pendingPaymentOrder');
        sessionStorage.removeItem('checkoutCart');
        sessionStorage.removeItem('checkoutShipping');
        sessionStorage.removeItem('checkoutAddress');

        // Set success status
        setStatus('success');
        setMessage('Payment successful! Your order has been placed.');
        
        // Show success toast
        toast({
          title: 'Payment Successful!',
          description: 'Your order has been placed successfully.',
          variant: 'default'
        });

        // Redirect to orders page after delay
        setTimeout(() => {
          navigate('/profile/orders');
        }, 3000);

      } catch (error) {
        console.error('❌ Error processing payment success:', error);
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Failed to process payment. Please contact support if money was deducted.');
        
        toast({
          title: 'Payment Processing Error',
          description: 'There was an issue processing your payment. Please contact support if money was deducted.',
          variant: 'destructive'
        });
      } finally {
        setIsProcessing(false);
      }
    };

    processPaymentSuccess();
  }, [searchParams, isAuthenticated, user, navigate, clearCart]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        {status === 'processing' && (
          <>
            <Loader2 className="h-16 w-16 text-blue-600 animate-spin mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Processing Payment</h1>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            {orderId && (
              <p className="text-sm text-gray-500">Order ID: {orderId}</p>
            )}
            <p className="text-sm text-gray-500 mt-4">Redirecting to your orders...</p>
          </>
        )}

        {status === 'error' && (
          <>
            <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Payment Error</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            <button
              onClick={() => navigate('/cart')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Return to Cart
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentSuccess;
