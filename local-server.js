import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createRequire } from 'module';

// Load environment variables
dotenv.config();

const require = createRequire(import.meta.url);
const app = express();
const PORT = process.env.VITE_SERVER_PORT || 3001;

// Middleware
app.use(cors({
  origin: true, // Allow all origins for development
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Import Razorpay API handlers
const createOrderHandler = require('./api/razorpay/create-order.cjs');
const verifyPaymentHandler = require('./api/razorpay/verify-payment.cjs');
const webhookHandler = require('./api/razorpay/webhook.cjs');

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Local Razorpay server is running',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Razorpay API routes
app.post('/api/razorpay/create-order', (req, res) => {
  console.log('📋 Received create-order request');
  createOrderHandler(req, res);
});

app.post('/api/razorpay/verify-payment', (req, res) => {
  console.log('🔐 Received verify-payment request');
  verifyPaymentHandler(req, res);
});

app.post('/api/razorpay/webhook', (req, res) => {
  console.log('🔔 Received webhook request');
  webhookHandler(req, res);
});

// Test endpoint to verify Razorpay credentials
app.get('/api/test/razorpay', (req, res) => {
  const hasKeyId = !!process.env.VITE_RAZORPAY_KEY_ID;
  const hasSecret = !!process.env.RAZORPAY_SECRET;
  const hasSupabaseUrl = !!process.env.VITE_SUPABASE_URL;
  const hasSupabaseKey = !!process.env.VITE_SUPABASE_ANON_KEY;

  res.json({
    status: 'Test Results',
    credentials: {
      razorpay_key_id: hasKeyId ? 'Present' : 'Missing',
      razorpay_secret: hasSecret ? 'Present' : 'Missing',
      supabase_url: hasSupabaseUrl ? 'Present' : 'Missing',
      supabase_anon_key: hasSupabaseKey ? 'Present' : 'Missing'
    },
    ready: hasKeyId && hasSecret && hasSupabaseUrl && hasSupabaseKey
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Server error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Local Razorpay server started');
  console.log(`📍 Server running on http://localhost:${PORT}`);
  console.log('🔧 Available endpoints:');
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   GET  http://localhost:${PORT}/api/test/razorpay`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/create-order`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/verify-payment`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/webhook`);
  console.log('');
  console.log('💡 Use "npm run dev:local" to run both server and frontend');
  console.log('');
});

export default app;
