import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { XCircle, RefreshCw } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

/**
 * Payment Failure Page
 * Handles mobile payment failures from Razorpay UPI apps
 */
const PaymentFailure: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const orderIdParam = searchParams.get('order_id');
  const errorCode = searchParams.get('error_code');
  const errorDescription = searchParams.get('error_description');

  useEffect(() => {
    console.log('📱 Mobile payment failure page loaded');
    console.log('📋 Failure parameters:', {
      orderIdParam,
      errorCode,
      errorDescription
    });

    // Clean up stored payment data
    sessionStorage.removeItem('pendingPaymentOrder');

    // Show failure toast
    toast({
      title: 'Payment Failed',
      description: errorDescription || 'Your payment could not be processed. Please try again.',
      variant: 'destructive'
    });
  }, [orderIdParam, errorCode, errorDescription]);

  const handleRetryPayment = () => {
    // Redirect back to checkout to retry payment
    navigate('/checkout');
  };

  const handleGoToCart = () => {
    // Redirect to cart
    navigate('/cart');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
        
        <h1 className="text-xl font-semibold text-gray-900 mb-2">Payment Failed</h1>
        
        <p className="text-gray-600 mb-4">
          {errorDescription || 'Your payment could not be processed. This could be due to insufficient funds, network issues, or payment cancellation.'}
        </p>

        {errorCode && (
          <p className="text-sm text-gray-500 mb-4">
            Error Code: {errorCode}
          </p>
        )}

        <div className="space-y-3">
          <button
            onClick={handleRetryPayment}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry Payment
          </button>
          
          <button
            onClick={handleGoToCart}
            className="w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Return to Cart
          </button>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Need Help?</h3>
          <p className="text-xs text-blue-700">
            If you continue to face issues, please contact our support team. 
            No money has been deducted from your account for failed payments.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailure;
