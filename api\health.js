module.exports = async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const healthData = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown',
      vercel: !!process.env.VERCEL,
      razorpay: {
        hasKeyId: !!process.env.RAZORPAY_KEY_ID,
        hasViteKeyId: !!process.env.VITE_RAZORPAY_KEY_ID,
        hasSecret: !!process.env.RAZORPAY_SECRET,
        keyIdPreview: process.env.RAZORPAY_KEY_ID ? process.env.RAZORPAY_KEY_ID.substring(0, 10) + '...' : 'missing',
        viteKeyIdPreview: process.env.VITE_RAZORPAY_KEY_ID ? process.env.VITE_RAZORPAY_KEY_ID.substring(0, 10) + '...' : 'missing'
      },
      supabase: {
        hasUrl: !!process.env.VITE_SUPABASE_URL,
        hasAnonKey: !!process.env.VITE_SUPABASE_ANON_KEY,
        urlPreview: process.env.VITE_SUPABASE_URL ? process.env.VITE_SUPABASE_URL.substring(0, 30) + '...' : 'missing'
      },
      allEnvKeys: Object.keys(process.env).filter(key => 
        key.includes('RAZORPAY') || 
        key.includes('SUPABASE') || 
        key.includes('VERCEL') ||
        key.includes('NODE_ENV')
      ).sort()
    };

    console.log('🏥 Health check requested:', healthData);
    return res.status(200).json(healthData);
  } catch (error) {
    console.error('❌ Health check error:', error);
    return res.status(500).json({
      status: 'ERROR',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};
