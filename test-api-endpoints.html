<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Endpoint Tester</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .loading { opacity: 0.6; }
    </style>
</head>
<body>
    <h1>🧪 Razorpay API Endpoint Tester</h1>
    <p>This tool tests the local API endpoints to ensure they're working correctly.</p>

    <div class="test-section">
        <h3>1. Health Check</h3>
        <button onclick="testHealthCheck()">Test Health Check</button>
        <div id="health-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Create Razorpay Order</h3>
        <button onclick="testCreateOrder()">Test Create Order</button>
        <div id="order-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Payment Verification (with dummy data)</h3>
        <button onclick="testVerifyPayment()">Test Verify Payment</button>
        <div id="verify-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Frontend Integration Test</h3>
        <button onclick="testFrontendIntegration()">Test Frontend Integration</button>
        <div id="frontend-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';

        async function testHealthCheck() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            resultDiv.className = 'loading';

            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                
                if (response.ok && data.status === 'OK') {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML = `
                        <h4>✅ Health Check Passed</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error('Health check failed');
                }
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `
                    <h4>❌ Health Check Failed</h4>
                    <p>Error: ${error.message}</p>
                    <p>Make sure the local server is running: <code>npm run dev:local</code></p>
                `;
            }
        }

        async function testCreateOrder() {
            const resultDiv = document.getElementById('order-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            resultDiv.className = 'loading';

            try {
                const response = await fetch(`${API_BASE}/api/razorpay/create-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: 100, // ₹100
                        currency: 'INR',
                        receipt: 'test_receipt_' + Date.now(),
                        notes: {
                            test: true,
                            user_id: 'test_user_' + Date.now()
                        }
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML = `
                        <h4>✅ Order Creation Passed</h4>
                        <p><strong>Order ID:</strong> ${data.data.id}</p>
                        <p><strong>Amount:</strong> ₹${data.data.amount / 100}</p>
                        <p><strong>Status:</strong> ${data.data.status}</p>
                        <details>
                            <summary>Full Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                    
                    // Store order ID for verification test
                    window.testOrderId = data.data.id;
                } else {
                    throw new Error(data.error || 'Order creation failed');
                }
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `
                    <h4>❌ Order Creation Failed</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        async function testVerifyPayment() {
            const resultDiv = document.getElementById('verify-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            resultDiv.className = 'loading';

            if (!window.testOrderId) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `
                    <h4>❌ No Order ID Available</h4>
                    <p>Please run "Test Create Order" first to get an order ID.</p>
                `;
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/razorpay/verify-payment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        razorpay_order_id: window.testOrderId,
                        razorpay_payment_id: 'pay_test_' + Date.now(),
                        razorpay_signature: 'dummy_signature_for_test',
                        order_id: 'test_order_' + Date.now()
                    })
                });

                const data = await response.json();
                
                // This should fail because we're using a dummy signature
                if (!response.ok || !data.success) {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML = `
                        <h4>✅ Verification Test Passed (Expected Failure)</h4>
                        <p>The verification correctly failed with invalid signature.</p>
                        <p><strong>Error:</strong> ${data.error}</p>
                        <p>This confirms the signature verification is working properly.</p>
                    `;
                } else {
                    resultDiv.className = 'error';
                    resultDiv.innerHTML = `
                        <h4>❌ Verification Test Failed</h4>
                        <p>The verification should have failed with dummy signature, but it passed.</p>
                        <p>This indicates a security issue with signature verification.</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `
                    <h4>❌ Verification Test Error</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        async function testFrontendIntegration() {
            const resultDiv = document.getElementById('frontend-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            resultDiv.className = 'loading';

            try {
                // Test if Razorpay script can be loaded
                const script = document.createElement('script');
                script.src = 'https://checkout.razorpay.com/v1/checkout.js';
                
                script.onload = () => {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML = `
                        <h4>✅ Frontend Integration Ready</h4>
                        <p>✅ Razorpay script loaded successfully</p>
                        <p>✅ API endpoints are accessible</p>
                        <p>✅ CORS is configured correctly</p>
                        <p><strong>Ready for payment testing!</strong></p>
                        <p>You can now test the complete payment flow on the main website.</p>
                    `;
                };

                script.onerror = () => {
                    resultDiv.className = 'error';
                    resultDiv.innerHTML = `
                        <h4>❌ Frontend Integration Failed</h4>
                        <p>Failed to load Razorpay script. Check internet connection.</p>
                    `;
                };

                document.head.appendChild(script);
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `
                    <h4>❌ Frontend Integration Error</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        // Auto-run health check on page load
        window.onload = () => {
            testHealthCheck();
        };
    </script>
</body>
</html>
