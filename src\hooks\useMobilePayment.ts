import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Order } from '@/services/orderService';
import { useAuth } from '@/context/SupabaseAuthContext';
import { createRazorpayOrder } from '@/services/payment/razorpayService';
import { toast } from 'sonner';
import MobilePaymentService, { StoredPaymentData } from '@/services/mobilePaymentService';

interface MobilePaymentOptions {
  onSuccess?: (paymentId: string, orderId: string) => void;
  onError?: (error: string) => void;
}

/**
 * Enhanced Mobile Payment Hook
 * Handles mobile UPI payments with reliable verification
 */
export const useMobilePayment = (options: MobilePaymentOptions = {}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();

  const isMobileDevice = useCallback(() => {
    return MobilePaymentService.isMobileDevice();
  }, []);

  const processPayment = useCallback(async (order: Order) => {
    if (!user) {
      toast.error('Please log in to continue with payment');
      return;
    }

    setIsLoading(true);

    try {
      console.log('🚀 Starting mobile payment process for order:', order.id);

      // Create Razorpay order
      const razorpayOrderResponse = await createRazorpayOrder(
        order.total_amount,
        'INR',
        `order_${order.id}`,
        {
          order_id: order.id,
          user_id: user.id,
          mobile_payment: true
        }
      );

      if (!razorpayOrderResponse.success) {
        throw new Error(razorpayOrderResponse.error || 'Failed to create payment order');
      }

      const razorpayOrderId = razorpayOrderResponse.data.id;
      console.log('✅ Razorpay order created:', razorpayOrderId);

      // Store comprehensive payment data for mobile verification
      const paymentData: StoredPaymentData = {
        orderId: order.id,
        razorpayOrderId: razorpayOrderId,
        amount: order.total_amount,
        userId: user.id,
        timestamp: Date.now(),
        status: 'initiated',
        // Store all necessary order data for reconstruction
        orderData: {
          items: order.items,
          shipping_address: order.shipping_address,
          billing_address: order.billing_address,
          shipping_fee: order.shipping_fee || 0,
          payment_method: 'Online Payment'
        }
      };

      // Use service to store payment data
      MobilePaymentService.storePaymentData(paymentData);

      // Configure Razorpay for mobile
      const razorpayOptions = {
        key: import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: order.total_amount * 100,
        currency: 'INR',
        name: 'The Badhees',
        description: `Order #${order.id.substring(0, 8)}`,
        order_id: razorpayOrderId,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email,
          contact: order.shipping_address?.phone || '',
        },
        notes: {
          order_id: order.id,
          user_id: user.id,
          mobile_payment: 'true'
        },
        theme: {
          color: '#3B82F6',
        },
        handler: function (response: any) {
          console.log('💳 Payment response received:', response);
          handlePaymentResponse(response, paymentData);
        },
        modal: {
          ondismiss: function () {
            console.log('📱 Payment modal dismissed');
            handlePaymentDismissal(paymentData);
          },
          confirm_close: false,
          escape: false,
          animation: false,
        },
      };

      // Open Razorpay
      const razorpay = new (window as any).Razorpay(razorpayOptions);
      razorpay.open();

    } catch (error) {
      console.error('❌ Error in mobile payment process:', error);
      setIsLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      toast.error(errorMessage);
      options.onError?.(errorMessage);
    }
  }, [user, navigate, options]);

  const handlePaymentResponse = useCallback(async (response: any, paymentData: any) => {
    console.log('🎉 Payment successful, processing response...');
    
    try {
      // Update payment data with response
      MobilePaymentService.updatePaymentData({
        razorpayPaymentId: response.razorpay_payment_id,
        razorpaySignature: response.razorpay_signature,
        status: 'completed',
        completedAt: Date.now()
      });

      // For mobile devices, redirect to verification page
      if (isMobileDevice()) {
        console.log('📱 Mobile device detected, redirecting to verification...');
        window.location.href = MobilePaymentService.getVerificationUrl();
      } else {
        // For desktop, process immediately
        const updatedData = MobilePaymentService.getStoredPaymentData();
        if (updatedData) {
          await processPaymentVerification(updatedData);
        }
      }
    } catch (error) {
      console.error('❌ Error handling payment response:', error);
      toast.error('Payment completed but verification failed. Please contact support.');
    } finally {
      setIsLoading(false);
    }
  }, [isMobileDevice]);

  const handlePaymentDismissal = useCallback((paymentData: any) => {
    console.log('⚠️ Payment dismissed, setting up verification check...');
    
    // Update status to dismissed
    MobilePaymentService.updatePaymentData({
      status: 'dismissed',
      dismissedAt: Date.now()
    });
    
    // For mobile, redirect to verification page after a short delay
    if (isMobileDevice()) {
      setTimeout(() => {
        console.log('📱 Redirecting to verification after dismissal...');
        window.location.href = MobilePaymentService.getVerificationUrl(true);
      }, 2000);
    }
    
    setIsLoading(false);
  }, [isMobileDevice]);

  const processPaymentVerification = useCallback(async (paymentData: any) => {
    try {
      console.log('🔐 Processing payment verification...');

      const verifyResponse = await fetch('/api/razorpay/verify-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          razorpay_order_id: paymentData.razorpayOrderId,
          razorpay_payment_id: paymentData.razorpayPaymentId,
          razorpay_signature: paymentData.razorpaySignature,
          order_id: paymentData.orderId,
        }),
      });

      if (!verifyResponse.ok) {
        throw new Error(`Verification failed: ${verifyResponse.status}`);
      }

      const verifyData = await verifyResponse.json();
      
      if (!verifyData.success) {
        throw new Error(verifyData.error || 'Payment verification failed');
      }

      console.log('✅ Payment verified successfully');
      
      // Clear stored payment data
      MobilePaymentService.clearPaymentData();
      
      // Call success callback
      options.onSuccess?.(paymentData.razorpayPaymentId, paymentData.orderId);
      
      toast.success('Payment successful! Your order has been placed.');
      navigate('/profile/orders');

    } catch (error) {
      console.error('❌ Payment verification error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Verification failed';
      toast.error(errorMessage);
      options.onError?.(errorMessage);
    }
  }, [navigate, options]);

  return {
    processPayment,
    isLoading,
    isMobileDevice: isMobileDevice()
  };
};

export default useMobilePayment;
