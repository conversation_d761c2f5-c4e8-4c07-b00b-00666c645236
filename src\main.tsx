import { createRoot } from 'react-dom/client'
import { StrictMode } from 'react'
import { HelmetProvider } from 'react-helmet-async'
import App from './App.tsx'
import './index.css'
import { initBrowserCompatibility } from './utils/browserUtils'
import './utils/testPaymentAPI'

// Initialize browser compatibility and error suppression
initBrowserCompatibility();

// Wrap the app in StrictMode for better development experience
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <HelmetProvider>
      <App />
    </HelmetProvider>
  </StrictMode>
);
