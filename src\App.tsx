import { Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import AuthCallback from "@/pages/AuthCallback";

import { CartProvider } from "@/context/SupabaseCartContext";
import { AuthProvider } from "@/context/SupabaseAuthContext";
import ErrorBoundary from "@/components/ErrorBoundary";
import OfflineIndicator from "@/components/ui/offline-indicator";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { TanStackDevTools } from "@/components/ReactQueryDevTools";

// Loading component
import LoadingSpinner from "@/components/ui/loading-spinner";

// Eagerly loaded pages (critical for initial render)
import Index from "@/pages/Index";
import NotFound from "@/pages/NotFound";
import AuthRedirect from "@/pages/AuthRedirect";

// Lazily loaded pages (code-split)
// Public pages
const Products = lazy(() => import("@/pages/Products"));
const ProductDetail = lazy(() => import("@/pages/ProductDetail"));
const Cart = lazy(() => import("@/pages/Cart"));

// Debug components (only for development)
const ReviewsDebugger = lazy(() => import("@/components/debug/ReviewsDebugger"));
const TestCustomization = lazy(() => import("@/pages/TestCustomization"));
const EmojiVibesTest = lazy(() => import("@/components/debug/EmojiVibesTest"));
const Login = lazy(() => import("@/pages/Login"));
const Register = lazy(() => import("@/pages/Register"));
const ForgotPassword = lazy(() => import("@/pages/ForgotPassword"));
const ResetPassword = lazy(() => import("@/pages/ResetPassword"));
const EmailConfirmation = lazy(() => import("@/pages/EmailConfirmation"));
const Profile = lazy(() => import("@/pages/Profile"));
const About = lazy(() => import("@/pages/About"));
const Contact = lazy(() => import("@/pages/Contact"));
const FAQ = lazy(() => import("@/pages/FAQ"));
const ShippingReturns = lazy(() => import("@/pages/ShippingReturns"));
const CareInstructions = lazy(() => import("@/pages/CareInstructions"));
const Warranty = lazy(() => import("@/pages/Warranty"));
const Categories = lazy(() => import("@/pages/Categories"));
const Orders = lazy(() => import("@/pages/Orders"));
const OrderDetails = lazy(() => import("@/pages/OrderDetails"));
const Checkout = lazy(() => import("@/pages/Checkout"));
const PaymentStatusPage = lazy(() => import("@/pages/PaymentStatusPage"));

const PaymentSuccess = lazy(() => import("@/pages/PaymentSuccess"));
const PaymentFailure = lazy(() => import("@/pages/PaymentFailure"));
const PaymentVerify = lazy(() => import("@/pages/PaymentVerify"));
const Shop = lazy(() => import("@/pages/Shop"));
const CustomInteriorProjects = lazy(() => import("@/pages/CustomInteriorProjects"));
const CompletedProjects = lazy(() => import("@/pages/CompletedProjects"));
const CompletedProjectDetail = lazy(() => import("@/pages/CompletedProjectDetail"));
const PrivacyPolicy = lazy(() => import("@/pages/PrivacyPolicy"));
const TermsOfService = lazy(() => import("@/pages/TermsOfService"));


// Admin pages (grouped for better code splitting)
const AdminDashboard = lazy(() => import("@/pages/AdminDashboard"));
const AdminProducts = lazy(() => import("@/pages/AdminProducts"));
const AdminProductAdd = lazy(() => import("@/pages/AdminProductAdd"));
const AdminProductEdit = lazy(() => import("@/pages/AdminProductEdit"));
const AdminOrders = lazy(() => import("@/pages/AdminOrders"));
const AdminOrderDetails = lazy(() => import("@/pages/AdminOrderDetails"));
const AdminCustomers = lazy(() => import("@/pages/AdminCustomers"));
const AdminCompletedProjects = lazy(() => import("@/pages/AdminCompletedProjects"));
const AdminCompletedProjectNew = lazy(() => import("@/pages/AdminCompletedProjectNew"));
const AdminCompletedProjectEdit = lazy(() => import("@/pages/AdminCompletedProjectEdit"));

const AdminUserManagement = lazy(() => import("@/pages/AdminUserManagement"));
const AdminCustomizationRequests = lazy(() => import("@/pages/AdminCustomizationRequests"));
const AdminContactSubmissions = lazy(() => import("@/pages/AdminContactSubmissions"));
const AdminConsultationRequests = lazy(() => import("@/pages/AdminConsultationRequests"));
const AdminSettings = lazy(() => import("@/pages/AdminSettings"));

// Employee Management pages
const AdminEmployees = lazy(() => import("@/pages/AdminEmployees"));
const AdminEmployeeForm = lazy(() => import("@/pages/AdminEmployeeForm"));
const AdminEmployeeDashboard = lazy(() => import("@/pages/AdminEmployeeDashboard"));
const AdminAttendance = lazy(() => import("@/pages/AdminAttendance"));
const AdminLeave = lazy(() => import("@/pages/AdminLeave"));
const AdminOvertime = lazy(() => import("@/pages/AdminOvertime"));
const AdminPayroll = lazy(() => import("@/pages/AdminPayroll"));

// Create a QueryClient for React Query with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true, // Enable refetch on window focus for fresh data
      refetchOnReconnect: true, // Refetch when network reconnects
      refetchOnMount: true, // Always refetch when component mounts
      staleTime: 1 * 60 * 1000, // 1 minute - shorter for fresher data on tab switch
      gcTime: 10 * 60 * 1000, // 10 minutes - keep data in cache longer (replaces cacheTime)
      retry: 2, // Retry failed requests twice
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      refetchInterval: false, // Disable automatic polling by default
      refetchIntervalInBackground: false, // Don't refetch in background
    },
    mutations: {
      retry: 1, // Retry mutations once
    },
  },
});

const App = () => (
  <BrowserRouter>
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <CartProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <OfflineIndicator />
              <TanStackDevTools />
              <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Index />} />
              <Route path="/products" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading products..." />}>
                  <Products />
                </Suspense>
              } />
              <Route path="/products/category/:categoryId" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading products..." />}>
                  <Products />
                </Suspense>
              } />
              <Route path="/products/:id" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading product details..." />}>
                  <ProductDetail />
                </Suspense>
              } />
              <Route path="/categories" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading categories..." />}>
                  <Categories />
                </Suspense>
              } />
              <Route path="/collections" element={<Navigate to="/custom-interiors" replace />} />
              <Route path="/collections/:id" element={<Navigate to="/custom-interiors" replace />} />
              <Route path="/custom-interiors" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading custom interiors..." />}>
                  <CustomInteriorProjects />
                </Suspense>
              } />
              <Route path="/completed-projects/:categoryId" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading projects..." />}>
                  <CompletedProjects />
                </Suspense>
              } />
              <Route path="/completed-projects/detail/:projectId" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading project details..." />}>
                  <CompletedProjectDetail />
                </Suspense>
              } />
              <Route path="/cart" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading cart..." />}>
                  <Cart />
                </Suspense>
              } />
              <Route path="/login" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading login..." />}>
                  <Login />
                </Suspense>
              } />
              <Route path="/register" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading registration..." />}>
                  <Register />
                </Suspense>
              } />
              <Route path="/forgot-password" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading..." />}>
                  <ForgotPassword />
                </Suspense>
              } />
              <Route path="/reset-password" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading..." />}>
                  <ResetPassword />
                </Suspense>
              } />
              <Route path="/confirm-email" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Confirming email..." />}>
                  <EmailConfirmation />
                </Suspense>
              } />
              <Route path="/about" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading about page..." />}>
                  <About />
                </Suspense>
              } />
              <Route path="/contact" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading contact page..." />}>
                  <Contact />
                </Suspense>
              } />
              <Route path="/faq" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading FAQ..." />}>
                  <FAQ />
                </Suspense>
              } />
              <Route path="/shipping-returns" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading shipping information..." />}>
                  <ShippingReturns />
                </Suspense>
              } />
              <Route path="/care-instructions" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading care instructions..." />}>
                  <CareInstructions />
                </Suspense>
              } />
              <Route path="/warranty" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading warranty information..." />}>
                  <Warranty />
                </Suspense>
              } />
              <Route path="/profile" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading profile..." />}>
                  <Profile />
                </Suspense>
              } />
              <Route path="/orders" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading orders..." />}>
                  <Orders />
                </Suspense>
              } />
              <Route path="/orders/:orderId" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading order details..." />}>
                  <OrderDetails />
                </Suspense>
              } />
              <Route path="/checkout" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading checkout..." />}>
                  <Checkout />
                </Suspense>
              } />
              <Route path="/payment-status/:orderId" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading payment status..." />}>
                  <PaymentStatusPage />
                </Suspense>
              } />

              <Route path="/payment-success" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Processing payment..." />}>
                  <PaymentSuccess />
                </Suspense>
              } />
              <Route path="/payment-failure" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading..." />}>
                  <PaymentFailure />
                </Suspense>
              } />
              <Route path="/payment-verify" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Verifying payment..." />}>
                  <PaymentVerify />
                </Suspense>
              } />
              <Route path="/shop" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading shop..." />}>
                  <Shop />
                </Suspense>
              } />

              {/* Debug routes - only for development */}
              <Route path="/debug/reviews" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading debugger..." />}>
                  <ReviewsDebugger />
                </Suspense>
              } />
              <Route path="/debug/customization" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading test..." />}>
                  <TestCustomization />
                </Suspense>
              } />
              <Route path="/debug/emoji-vibes" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading emoji vibes test..." />}>
                  <EmojiVibesTest />
                </Suspense>
              } />

              <Route path="/privacy-policy" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading privacy policy..." />}>
                  <PrivacyPolicy />
                </Suspense>
              } />

              <Route path="/terms-of-service" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Loading terms of service..." />}>
                  <TermsOfService />
                </Suspense>
              } />

              <Route path="/auth/callback" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Completing authentication..." />}>
                  <AuthCallback />
                </Suspense>
              } />
              <Route path="/auth/confirm" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Confirming authentication..." />}>
                  <AuthCallback />
                </Suspense>
              } />
              <Route path="/auth/redirect" element={
                <Suspense fallback={<LoadingSpinner size="lg" text="Processing authentication..." />}>
                  <AuthRedirect />
                </Suspense>
              } />



              {/* Protected Admin Routes */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading admin dashboard..." />}>
                      <AdminDashboard />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/dashboard"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading admin dashboard..." />}>
                      <AdminDashboard />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/products"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading admin products..." />}>
                      <AdminProducts />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/products/new"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading product form..." />}>
                      <AdminProductAdd />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/products/:productId/edit"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading product editor..." />}>
                      <AdminProductEdit />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/orders"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading admin orders..." />}>
                      <AdminOrders />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/orders/:orderId"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading order details..." />}>
                      <AdminOrderDetails />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/customers"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading customer management..." />}>
                      <AdminCustomers />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/customization-requests"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading customization requests..." />}>
                      <AdminCustomizationRequests />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/contact-submissions"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading contact submissions..." />}>
                      <AdminContactSubmissions />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/consultation-requests"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading consultation requests..." />}>
                      <AdminConsultationRequests />
                    </Suspense>
                  </ProtectedRoute>
                }
              />

              {/* Redirect old migration pages to completed projects */}
              <Route
                path="/admin/projects-migration"
                element={<Navigate to="/admin/completed-projects" replace />}
              />
              <Route
                path="/admin/migration"
                element={<Navigate to="/admin/completed-projects" replace />}
              />
              <Route
                path="/admin/users"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading user management..." />}>
                      <AdminUserManagement />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/settings"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading settings..." />}>
                      <AdminSettings />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/completed-projects"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading completed projects..." />}>
                      <AdminCompletedProjects />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/completed-projects/new"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading project form..." />}>
                      <AdminCompletedProjectNew />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/completed-projects/:projectId/edit"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading project editor..." />}>
                      <AdminCompletedProjectEdit />
                    </Suspense>
                  </ProtectedRoute>
                }
              />

              {/* Employee Management Routes */}
              <Route
                path="/admin/employees/dashboard"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading employee dashboard..." />}>
                      <AdminEmployeeDashboard />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/employees"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading employee management..." />}>
                      <AdminEmployees />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/employees/new"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading employee form..." />}>
                      <AdminEmployeeForm />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/employees/:employeeId"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading employee editor..." />}>
                      <AdminEmployeeForm />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/employees/attendance"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading attendance management..." />}>
                      <AdminAttendance />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/employees/leave"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading leave management..." />}>
                      <AdminLeave />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/employees/overtime"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading overtime management..." />}>
                      <AdminOvertime />
                    </Suspense>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/employees/payroll"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Suspense fallback={<LoadingSpinner size="lg" text="Loading payroll management..." />}>
                      <AdminPayroll />
                    </Suspense>
                  </ProtectedRoute>
                }
              />

              {/* Editor routes removed and redirected to admin */}
              <Route path="/editor" element={<Navigate to="/admin" replace />} />
              <Route path="/editor/products" element={<Navigate to="/admin" replace />} />

              <Route path="*" element={<NotFound />} />
              </Routes>
            </TooltipProvider>
          </CartProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  </BrowserRouter>
);

export default App;
