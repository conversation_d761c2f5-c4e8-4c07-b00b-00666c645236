/**
 * Payment Hook
 * Handles Razorpay payment processing
 */
import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Order } from '@/services/orderService';
import { 
  createRazorpayOrder, 
  verifyRazorpayPayment, 
  loadRazorpayScript,
  PaymentResponse 
} from '@/services/payment/razorpayService';

interface UsePaymentOptions {
  onSuccess?: (paymentId: string, orderId: string) => void;
  onFailure?: (error: any) => void;
}

export function usePayment(options: UsePaymentOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  /**
   * Process payment using Razorpay
   */
  const processPayment = async (order: Order) => {
    if (!user) {
      toast.error('You need to be logged in to make a payment.');
      return;
    }

    setIsLoading(true);

    try {
      // Load Razorpay script
      const scriptLoaded = await loadRazorpayScript();
      if (!scriptLoaded) {
        throw new Error('Failed to load payment gateway');
      }

      // Validate environment variables
      if (!import.meta.env.VITE_RAZORPAY_KEY_ID) {
        throw new Error('Missing VITE_RAZORPAY_KEY_ID environment variable');
      }

      // Create Razorpay order
      const razorpayOrder = await createRazorpayOrder(
        order.total_amount,
        'INR',
        order.id,
        {
          user_id: order.user_id,
          email: user.email,
          shipping_address: order.shipping_address
        }
      );

      // Detect mobile device
      const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // Configure Razorpay options
      const options: any = {
        key: import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: razorpayOrder.amount,
        currency: razorpayOrder.currency,
        name: 'The Badhees',
        description: `Order #${order.id.substring(0, 8)}`,
        order_id: razorpayOrder.id,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email,
          contact: order.shipping_address?.phone || '',
        },
        notes: {
          order_id: order.id,
          shipping_address: JSON.stringify(order.shipping_address),
        },
        theme: {
          color: '#3B82F6',
        },
        handler: function (response: PaymentResponse) {
          handlePaymentSuccess(response, order);
        },
        modal: {
          ondismiss: function () {
            setIsLoading(false);
            toast.info('Payment cancelled. You can try again later.');
          },
        },
      };

      // Add mobile-specific options
      if (isMobile) {
        console.log('📱 Mobile device detected - configuring for mobile payments');

        // Store order details for mobile redirect handling
        if (order.id.startsWith('temp_')) {
          // For temporary orders, store all necessary data
          sessionStorage.setItem('pendingPaymentOrder', JSON.stringify({
            orderId: order.id,
            razorpayOrderId: razorpayOrder.id,
            amount: order.total_amount,
            shippingFee: order.shipping_fee,
            shippingNotes: order.shipping_notes,
            timestamp: Date.now()
          }));

          // Store cart items and addresses for order creation
          sessionStorage.setItem('checkoutCart', JSON.stringify(order.items || []));
          sessionStorage.setItem('checkoutAddress', JSON.stringify({
            shipping: order.shipping_address,
            billing: order.billing_address
          }));
        }

        const currentUrl = window.location.origin;
        options.redirect = true;
        options.callback_url = `${currentUrl}/payment-success?order_id=${order.id}&razorpay_order_id=${razorpayOrder.id}`;
        options.cancel_url = `${currentUrl}/payment-failure?order_id=${order.id}`;

        console.log('📱 Mobile redirect URLs configured:', {
          callback_url: options.callback_url,
          cancel_url: options.cancel_url
        });
      }

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Error initializing payment:', error);
      setIsLoading(false);
      toast.error('Failed to initialize payment. Please try again.');

      if (options.onFailure) {
        options.onFailure(error);
      }
    }
  };

  /**
   * Handle successful payment
   */
  const handlePaymentSuccess = async (response: PaymentResponse, order: Order) => {
    try {
      // Verify payment
      const isVerified = await verifyRazorpayPayment(response, order.id);

      if (isVerified) {
        console.log('✅ Payment verified successfully');
        
        if (options.onSuccess) {
          options.onSuccess(response.razorpay_payment_id, order.id);
        }
      } else {
        throw new Error('Payment verification failed');
      }
    } catch (error) {
      console.error('Error verifying payment:', error);
      toast.error('Payment verification failed. Please contact support if money was deducted.');

      if (options.onFailure) {
        options.onFailure(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    processPayment,
    isLoading,
  };
}
