/**
 * Mock Payment Status API for Testing Mobile UPI Flow
 * This simulates the payment status checking without requiring real Razorpay credentials
 */

module.exports = async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { razorpay_order_id, order_id } = req.body;

    if (!razorpay_order_id) {
      return res.status(400).json({
        success: false,
        error: 'Razorpay order ID is required'
      });
    }

    console.log('🧪 Mock: Checking payment status for order:', razorpay_order_id);

    // Simulate different payment scenarios based on order ID
    let paymentStatus = 'pending';
    let paymentId = null;

    // Simulate payment success after 10 seconds for testing
    const orderCreatedTime = parseInt(razorpay_order_id.split('_').pop()) || Date.now();
    const timeSinceCreation = Date.now() - orderCreatedTime;

    if (timeSinceCreation > 10000) { // 10 seconds
      paymentStatus = 'captured';
      paymentId = `pay_mock_${Date.now()}`;
      console.log('✅ Mock: Payment successful after 10 seconds');
    } else {
      console.log('⏳ Mock: Payment still pending, will succeed after 10 seconds');
    }

    // Return mock payment status
    return res.status(200).json({
      success: true,
      payment_status: paymentStatus,
      payment_id: paymentId,
      order_status: paymentStatus === 'captured' ? 'paid' : 'created',
      amount_paid: paymentStatus === 'captured' ? 10000 : 0, // ₹100 in paise
      amount_due: paymentStatus === 'captured' ? 0 : 10000,
      payments_count: paymentStatus === 'captured' ? 1 : 0,
      mock: true,
      message: paymentStatus === 'captured' 
        ? 'Mock payment completed successfully' 
        : 'Mock payment pending - will complete in ' + Math.max(0, Math.ceil((10000 - timeSinceCreation) / 1000)) + ' seconds'
    });

  } catch (error) {
    console.error('❌ Mock payment status error:', error);
    
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to check payment status',
      mock: true
    });
  }
};
