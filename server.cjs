const http = require('http');
const url = require('url');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const PORT = process.env.VITE_SERVER_PORT || 3001;

// Import API handlers
const createOrderHandler = require('./api/razorpay/create-order.js');
const verifyPaymentHandler = require('./api/razorpay/verify-payment.js');
const webhookHandler = require('./api/razorpay/webhook.js');

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Create server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  console.log(`${method} ${path}`);

  try {
    // Parse request body for POST requests
    if (method === 'POST') {
      req.body = await parseBody(req);
    }

    // Route handling
    if (path === '/health') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'OK',
        message: 'Razorpay server is running',
        timestamp: new Date().toISOString(),
        port: PORT
      }));
    } else if (path === '/api/test/razorpay') {
      const hasKeyId = !!process.env.VITE_RAZORPAY_KEY_ID;
      const hasSecret = !!process.env.RAZORPAY_SECRET;
      const hasSupabaseUrl = !!process.env.VITE_SUPABASE_URL;
      const hasSupabaseKey = !!process.env.VITE_SUPABASE_ANON_KEY;

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'Test Results',
        credentials: {
          razorpay_key_id: hasKeyId ? 'Present' : 'Missing',
          razorpay_secret: hasSecret ? 'Present' : 'Missing',
          supabase_url: hasSupabaseUrl ? 'Present' : 'Missing',
          supabase_anon_key: hasSupabaseKey ? 'Present' : 'Missing'
        },
        ready: hasKeyId && hasSecret && hasSupabaseUrl && hasSupabaseKey
      }));
    } else if (path === '/api/razorpay/create-order' && method === 'POST') {
      console.log('📋 Handling create-order request');
      await createOrderHandler(req, res);
    } else if (path === '/api/razorpay/verify-payment' && method === 'POST') {
      console.log('🔐 Handling verify-payment request');
      await verifyPaymentHandler(req, res);
    } else if (path === '/api/razorpay/webhook' && method === 'POST') {
      console.log('🔔 Handling webhook request');
      await webhookHandler(req, res);
    } else {
      // 404 Not Found
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        error: 'Endpoint not found',
        path: path
      }));
    }
  } catch (error) {
    console.error('❌ Server error:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      error: 'Internal server error',
      message: error.message
    }));
  }
});

// Start server
server.listen(PORT, () => {
  console.log('🚀 Razorpay server started successfully!');
  console.log(`📍 Server running on http://localhost:${PORT}`);
  console.log('🔧 Available endpoints:');
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   GET  http://localhost:${PORT}/api/test/razorpay`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/create-order`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/verify-payment`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/webhook`);
  console.log('');
  console.log('✅ Ready for mobile payment testing!');
  console.log('💡 Use "npm run dev:mobile" to run both server and frontend');
  console.log('');
});

module.exports = server;
