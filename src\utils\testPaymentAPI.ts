/**
 * Test Payment API Utility
 * Use this to test if the payment API is working correctly
 */

export const testPaymentAPI = async () => {
  try {
    console.log('🧪 Testing Payment API...');
    
    const apiBaseUrl = window.location.origin;
    console.log('🌐 API Base URL:', apiBaseUrl);
    
    const testPayload = {
      amount: 100, // ₹1.00 for testing
      currency: 'INR',
      receipt: 'test_receipt_' + Date.now(),
      notes: {
        test: true,
        user_id: 'test_user'
      }
    };
    
    console.log('📤 Test payload:', testPayload);
    
    const response = await fetch(`${apiBaseUrl}/api/razorpay/create-order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(testPayload),
    });
    
    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return { success: false, error: `HTTP ${response.status}: ${errorText}` };
    }
    
    const result = await response.json();
    console.log('✅ API Response:', result);
    
    return { success: true, data: result };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return { success: false, error: error.message };
  }
};

// Add to window for easy testing in console
if (typeof window !== 'undefined') {
  (window as any).testPaymentAPI = testPaymentAPI;
}
