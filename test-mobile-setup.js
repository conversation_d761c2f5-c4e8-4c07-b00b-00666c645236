import dotenv from 'dotenv';
import os from 'os';

// Load environment variables
dotenv.config();

console.log('🔍 Mobile Payment Testing Setup Check\n');

// Check environment variables
const checks = {
  razorpayKeyId: !!process.env.VITE_RAZORPAY_KEY_ID,
  razorpaySecret: !!process.env.RAZORPAY_SECRET,
  supabaseUrl: !!process.env.VITE_SUPABASE_URL,
  supabaseKey: !!process.env.VITE_SUPABASE_ANON_KEY,
};

console.log('📋 Environment Variables:');
console.log(`   VITE_RAZORPAY_KEY_ID: ${checks.razorpayKeyId ? '✅ Present' : '❌ Missing'}`);
console.log(`   RAZORPAY_SECRET: ${checks.razorpaySecret ? '✅ Present' : '❌ Missing'}`);
console.log(`   VITE_SUPABASE_URL: ${checks.supabaseUrl ? '✅ Present' : '❌ Missing'}`);
console.log(`   VITE_SUPABASE_ANON_KEY: ${checks.supabaseKey ? '✅ Present' : '❌ Missing'}`);
console.log('');

// Check network interfaces
console.log('🌐 Network Information:');
const interfaces = os.networkInterfaces();
const addresses = [];

Object.keys(interfaces).forEach(name => {
  interfaces[name].forEach(iface => {
    if (iface.family === 'IPv4' && !iface.internal) {
      addresses.push({
        name: name,
        address: iface.address
      });
    }
  });
});

if (addresses.length > 0) {
  console.log('   Available IP addresses for mobile testing:');
  addresses.forEach(addr => {
    console.log(`   📱 ${addr.name}: http://${addr.address}:3000 (Vercel) or http://${addr.address}:8080 (Vite)`);
  });
} else {
  console.log('   ⚠️  No external IP addresses found');
}
console.log('');

// Check if all requirements are met
const allGood = Object.values(checks).every(check => check) && addresses.length > 0;

if (allGood) {
  console.log('🎉 Setup Check: PASSED');
  console.log('');
  console.log('📱 Ready for Mobile Testing!');
  console.log('');
  console.log('🚀 Next Steps:');
  console.log('   1. Run: npm run dev:vercel');
  console.log('   2. Open mobile browser');
  console.log('   3. Navigate to one of the IP addresses above');
  console.log('   4. Test the payment flow');
  console.log('');
} else {
  console.log('❌ Setup Check: FAILED');
  console.log('');
  console.log('🔧 Issues to Fix:');
  
  if (!checks.razorpayKeyId || !checks.razorpaySecret) {
    console.log('   • Missing Razorpay credentials in .env file');
  }
  
  if (!checks.supabaseUrl || !checks.supabaseKey) {
    console.log('   • Missing Supabase credentials in .env file');
  }
  
  if (addresses.length === 0) {
    console.log('   • No network connection found for mobile testing');
  }
  
  console.log('');
  console.log('📝 Please fix the issues above and run this test again');
  console.log('');
}

// Additional recommendations
console.log('💡 Testing Tips:');
console.log('   • Ensure mobile and computer are on same WiFi');
console.log('   • Use Chrome or Safari on mobile for best compatibility');
console.log('   • Test with different UPI apps (GPay, PhonePe, Paytm)');
console.log('   • Check browser console for any errors');
console.log('   • Verify orders appear in admin dashboard after payment');
console.log('');
