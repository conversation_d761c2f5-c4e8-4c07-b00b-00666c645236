const http = require('http');
const url = require('url');
const querystring = require('querystring');
const crypto = require('crypto');
require('dotenv').config();

const PORT = process.env.VITE_SERVER_PORT || 3001;

// Razorpay setup
const Razorpay = require('razorpay');
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || process.env.VITE_RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_SECRET,
});

// Supabase setup
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, { 
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'true'
  });
  res.end(JSON.stringify(data));
}

// Create Razorpay Order Handler
async function handleCreateOrder(req, res) {
  try {
    console.log('💰 Creating Razorpay order');
    const { amount, currency = 'INR', receipt, notes = {} } = req.body;
    console.log('📋 Order details:', { amount, currency, receipt, notes });

    if (!amount || !receipt) {
      console.error('❌ Missing required parameters');
      return sendJSON(res, 400, {
        success: false,
        error: 'Amount and receipt are required'
      });
    }

    const options = {
      amount: Math.round(amount * 100), // Convert to paise
      currency,
      receipt,
      notes,
    };

    console.log('🔧 Razorpay options:', options);
    const order = await razorpay.orders.create(options);
    console.log('✅ Razorpay order created successfully:', order.id);

    // Store payment record in database
    const { error: paymentError } = await supabase
      .from('razorpay_payments')
      .insert({
        razorpay_order_id: order.id,
        amount: amount,
        currency: currency,
        receipt: receipt,
        status: 'created',
        created_at: new Date().toISOString()
      });

    if (paymentError) {
      console.error('❌ Error storing payment record:', paymentError);
    } else {
      console.log('✅ Payment record stored successfully');
    }

    return sendJSON(res, 200, {
      success: true,
      data: order
    });
  } catch (error) {
    console.error('❌ Error creating Razorpay order:', error);
    return sendJSON(res, 500, {
      success: false,
      error: error.message || 'Failed to create Razorpay order'
    });
  }
}

// Verify Payment Handler
async function handleVerifyPayment(req, res) {
  try {
    console.log('🔐 Verifying payment');
    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      order_id
    } = req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.error('❌ Missing required parameters');
      return sendJSON(res, 400, {
        success: false,
        error: 'Payment verification failed: Missing required parameters'
      });
    }

    // Verify signature
    const secret = process.env.RAZORPAY_SECRET;
    const shasum = crypto.createHmac('sha256', secret);
    shasum.update(`${razorpay_order_id}|${razorpay_payment_id}`);
    const digest = shasum.digest('hex');

    console.log('🔐 Generated signature:', digest);
    console.log('📨 Received signature:', razorpay_signature);

    if (digest !== razorpay_signature) {
      console.error('❌ Invalid signature');
      return sendJSON(res, 400, {
        success: false,
        error: 'Payment verification failed: Invalid signature'
      });
    }

    console.log('✅ Payment signature verified successfully');

    // Update payment record
    const { error: paymentError } = await supabase
      .from('razorpay_payments')
      .update({
        razorpay_payment_id: razorpay_payment_id,
        status: 'captured',
        method: 'online',
        updated_at: new Date().toISOString()
      })
      .eq('razorpay_order_id', razorpay_order_id);

    if (paymentError) {
      console.error('❌ Error updating payment record:', paymentError);
    } else {
      console.log('✅ Payment record updated successfully');
    }

    // Update order if order_id is provided
    if (order_id && !order_id.startsWith('temp_')) {
      console.log('📦 Updating order with payment details:', order_id);
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          razorpay_payment_id: razorpay_payment_id,
          payment_status: 'paid',
          status: 'paid',
          updated_at: new Date().toISOString()
        })
        .eq('id', order_id);

      if (orderError) {
        console.error('❌ Error updating order:', orderError);
      } else {
        console.log('✅ Order updated successfully');
      }
    }

    return sendJSON(res, 200, {
      success: true,
      message: 'Payment verified successfully',
      order_id: order_id,
      payment_id: razorpay_payment_id
    });
  } catch (error) {
    console.error('❌ Error verifying payment:', error);
    return sendJSON(res, 500, {
      success: false,
      error: error.message || 'Failed to verify payment'
    });
  }
}

// Create server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  console.log(`${method} ${path}`);

  try {
    // Parse request body for POST requests
    if (method === 'POST') {
      req.body = await parseBody(req);
    }

    // Route handling
    if (path === '/health') {
      return sendJSON(res, 200, {
        status: 'OK',
        message: 'Razorpay server is running',
        timestamp: new Date().toISOString(),
        port: PORT
      });
    } else if (path === '/api/test/razorpay') {
      const hasKeyId = !!(process.env.VITE_RAZORPAY_KEY_ID || process.env.RAZORPAY_KEY_ID);
      const hasSecret = !!process.env.RAZORPAY_SECRET;
      const hasSupabaseUrl = !!process.env.VITE_SUPABASE_URL;
      const hasSupabaseKey = !!process.env.VITE_SUPABASE_ANON_KEY;

      return sendJSON(res, 200, {
        status: 'Test Results',
        credentials: {
          razorpay_key_id: hasKeyId ? 'Present' : 'Missing',
          razorpay_secret: hasSecret ? 'Present' : 'Missing',
          supabase_url: hasSupabaseUrl ? 'Present' : 'Missing',
          supabase_anon_key: hasSupabaseKey ? 'Present' : 'Missing'
        },
        ready: hasKeyId && hasSecret && hasSupabaseUrl && hasSupabaseKey
      });
    } else if (path === '/api/razorpay/create-order' && method === 'POST') {
      console.log('📋 Handling create-order request');
      return await handleCreateOrder(req, res);
    } else if (path === '/api/razorpay/verify-payment' && method === 'POST') {
      console.log('🔐 Handling verify-payment request');
      return await handleVerifyPayment(req, res);
    } else if (path === '/api/razorpay/webhook' && method === 'POST') {
      console.log('🔔 Handling webhook request');
      // Simple webhook handler
      return sendJSON(res, 200, { success: true, message: 'Webhook received' });
    } else {
      // 404 Not Found
      return sendJSON(res, 404, {
        success: false,
        error: 'Endpoint not found',
        path: path
      });
    }
  } catch (error) {
    console.error('❌ Server error:', error);
    return sendJSON(res, 500, {
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

// Start server
server.listen(PORT, () => {
  console.log('🚀 Razorpay Server Started Successfully!');
  console.log(`📍 Server running on http://localhost:${PORT}`);
  console.log('🔧 Available endpoints:');
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   GET  http://localhost:${PORT}/api/test/razorpay`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/create-order`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/verify-payment`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/webhook`);
  console.log('');
  console.log('✅ Ready for mobile payment testing!');
  console.log('💡 In another terminal, run: npm run dev');
  console.log('📱 Then access from mobile: http://YOUR_IP:8080');
  console.log('');
});

module.exports = server;
