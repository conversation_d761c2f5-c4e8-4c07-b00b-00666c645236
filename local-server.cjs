const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:8080', 'http://localhost:8081', 'http://localhost:8082'],
  credentials: true
}));
app.use(express.json());

// Import API routes
const createOrderHandler = require('./api/razorpay/create-order.cjs');
const verifyPaymentHandler = require('./api/razorpay/verify-payment.cjs');

// API Routes
app.post('/api/razorpay/create-order', createOrderHandler);
app.post('/api/razorpay/verify-payment', verifyPaymentHandler);

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: 'development',
    razorpay: {
      hasKeyId: !!process.env.RAZORPAY_KEY_ID,
      hasViteKeyId: !!process.env.VITE_RAZORPAY_KEY_ID,
      hasSecret: !!process.env.RAZORPAY_SECRET,
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Local server running on http://localhost:${PORT}`);
  console.log('📋 Available endpoints:');
  console.log('  - POST /api/razorpay/create-order');
  console.log('  - POST /api/razorpay/verify-payment');
  console.log('  - GET /api/health');
  console.log('');
  console.log('🔧 Environment check:');
  console.log('  - RAZORPAY_KEY_ID:', !!process.env.RAZORPAY_KEY_ID);
  console.log('  - VITE_RAZORPAY_KEY_ID:', !!process.env.VITE_RAZORPAY_KEY_ID);
  console.log('  - RAZORPAY_SECRET:', !!process.env.RAZORPAY_SECRET);
});
