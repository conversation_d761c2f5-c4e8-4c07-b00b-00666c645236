/**
 * Mobile Payment Service
 * Handles mobile-specific payment detection and recovery
 */

export interface StoredPaymentData {
  orderId: string;
  razorpayOrderId: string;
  amount: number;
  userId: string;
  timestamp: number;
  status: 'initiated' | 'completed' | 'dismissed' | 'captured';
  razorpayPaymentId?: string;
  razorpaySignature?: string;
  completedAt?: number;
  dismissedAt?: number;
  orderData?: {
    items: any[];
    shipping_address: any;
    billing_address: any;
    shipping_fee: number;
    payment_method: string;
  };
}

export class MobilePaymentService {
  private static readonly STORAGE_KEYS = {
    MOBILE_PAYMENT: 'mobilePaymentData',
    LAST_ATTEMPT: 'lastPaymentAttempt',
    PAYMENT_PREFIX: 'payment_'
  };

  private static readonly MAX_PAYMENT_AGE = 30 * 60 * 1000; // 30 minutes

  /**
   * Store payment data for mobile verification
   */
  static storePaymentData(data: StoredPaymentData): void {
    try {
      const serializedData = JSON.stringify(data);
      
      // Store in multiple locations for reliability
      sessionStorage.setItem(this.STORAGE_KEYS.MOBILE_PAYMENT, serializedData);
      localStorage.setItem(this.STORAGE_KEYS.LAST_ATTEMPT, serializedData);
      
      // Store with timestamp for recovery
      const timestampKey = `${this.STORAGE_KEYS.PAYMENT_PREFIX}${data.timestamp}`;
      localStorage.setItem(timestampKey, serializedData);
      
      console.log('💾 Payment data stored successfully');
    } catch (error) {
      console.error('❌ Error storing payment data:', error);
    }
  }

  /**
   * Retrieve stored payment data
   */
  static getStoredPaymentData(): StoredPaymentData | null {
    const sources = [
      () => sessionStorage.getItem(this.STORAGE_KEYS.MOBILE_PAYMENT),
      () => localStorage.getItem(this.STORAGE_KEYS.LAST_ATTEMPT),
      () => this.getLatestTimestampedPayment()
    ];

    for (const getSource of sources) {
      try {
        const data = getSource();
        if (data) {
          const parsed: StoredPaymentData = JSON.parse(data);
          
          // Check if data is recent
          if (Date.now() - parsed.timestamp < this.MAX_PAYMENT_AGE) {
            console.log('📦 Found valid payment data:', parsed.orderId);
            return parsed;
          } else {
            console.log('⏰ Payment data expired, age:', Date.now() - parsed.timestamp);
          }
        }
      } catch (error) {
        console.warn('⚠️ Error parsing payment data:', error);
      }
    }

    console.log('❌ No valid payment data found');
    return null;
  }

  /**
   * Update stored payment data
   */
  static updatePaymentData(updates: Partial<StoredPaymentData>): void {
    const existingData = this.getStoredPaymentData();
    if (!existingData) {
      console.warn('⚠️ No existing payment data to update');
      return;
    }

    const updatedData = { ...existingData, ...updates };
    this.storePaymentData(updatedData);
  }

  /**
   * Clear all payment data
   */
  static clearPaymentData(): void {
    try {
      // Clear session storage
      sessionStorage.removeItem(this.STORAGE_KEYS.MOBILE_PAYMENT);
      
      // Clear last attempt
      localStorage.removeItem(this.STORAGE_KEYS.LAST_ATTEMPT);
      
      // Clear timestamped payments
      Object.keys(localStorage)
        .filter(key => key.startsWith(this.STORAGE_KEYS.PAYMENT_PREFIX))
        .forEach(key => localStorage.removeItem(key));
      
      console.log('🧹 Payment data cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing payment data:', error);
    }
  }

  /**
   * Get the latest timestamped payment
   */
  private static getLatestTimestampedPayment(): string | null {
    try {
      const paymentKeys = Object.keys(localStorage)
        .filter(key => key.startsWith(this.STORAGE_KEYS.PAYMENT_PREFIX))
        .sort((a, b) => {
          const timestampA = parseInt(a.replace(this.STORAGE_KEYS.PAYMENT_PREFIX, ''));
          const timestampB = parseInt(b.replace(this.STORAGE_KEYS.PAYMENT_PREFIX, ''));
          return timestampB - timestampA; // Latest first
        });

      if (paymentKeys.length > 0) {
        return localStorage.getItem(paymentKeys[0]);
      }
    } catch (error) {
      console.warn('⚠️ Error getting latest timestamped payment:', error);
    }
    
    return null;
  }

  /**
   * Check if device is mobile
   */
  static isMobileDevice(): boolean {
    return window.innerWidth <= 768 || 
           /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * Check for pending payments on app startup
   */
  static checkForPendingPayments(): StoredPaymentData | null {
    const paymentData = this.getStoredPaymentData();
    
    if (paymentData && paymentData.status === 'initiated') {
      console.log('🔍 Found pending payment on startup:', paymentData.orderId);
      return paymentData;
    }
    
    return null;
  }

  /**
   * Clean up old payment data
   */
  static cleanupOldPayments(): void {
    try {
      const cutoffTime = Date.now() - this.MAX_PAYMENT_AGE;
      
      Object.keys(localStorage)
        .filter(key => key.startsWith(this.STORAGE_KEYS.PAYMENT_PREFIX))
        .forEach(key => {
          try {
            const data = localStorage.getItem(key);
            if (data) {
              const parsed: StoredPaymentData = JSON.parse(data);
              if (parsed.timestamp < cutoffTime) {
                localStorage.removeItem(key);
                console.log('🧹 Cleaned up old payment:', key);
              }
            }
          } catch (error) {
            // If we can't parse it, remove it
            localStorage.removeItem(key);
          }
        });
    } catch (error) {
      console.warn('⚠️ Error cleaning up old payments:', error);
    }
  }

  /**
   * Generate a unique session ID for payment tracking
   */
  static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if payment verification is needed
   */
  static needsVerification(): boolean {
    const paymentData = this.getStoredPaymentData();
    return paymentData !== null && 
           (paymentData.status === 'initiated' || paymentData.status === 'dismissed');
  }

  /**
   * Get verification URL with session parameters
   */
  static getVerificationUrl(dismissed: boolean = false): string {
    const sessionId = this.generateSessionId();
    const baseUrl = '/payment-verify';
    const params = new URLSearchParams({
      session: sessionId,
      ...(dismissed && { dismissed: 'true' })
    });
    
    return `${baseUrl}?${params.toString()}`;
  }
}

export default MobilePaymentService;
