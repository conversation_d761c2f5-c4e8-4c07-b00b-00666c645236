const http = require('http');
const url = require('url');
require('dotenv').config();

const PORT = process.env.VITE_SERVER_PORT || 3001;

// Import API handlers
const createOrderHandler = require('./api/razorpay/create-order.js');
const verifyPaymentHandler = require('./api/razorpay/verify-payment.js');

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to set CORS headers
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token, Origin');
  res.setHeader('Access-Control-Max-Age', '86400');
}

// Create server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`🌐 ${new Date().toISOString()} - ${method} ${path}`);

  // Set CORS headers for all requests
  setCORSHeaders(res);

  // Handle OPTIONS requests (preflight)
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    // Parse request body for POST requests
    if (method === 'POST') {
      req.body = await parseBody(req);
      console.log('📝 Request body:', JSON.stringify(req.body, null, 2));
    }

    // Route handling
    if (path === '/api/razorpay/create-order' && method === 'POST') {
      console.log('🚀 Handling create-order request');
      await createOrderHandler(req, res);
    } else if (path === '/api/razorpay/verify-payment' && method === 'POST') {
      console.log('🔍 Handling verify-payment request');
      await verifyPaymentHandler(req, res);
    } else if (path === '/api/health' && method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        message: 'Local API server is running',
        timestamp: new Date().toISOString(),
        environment: {
          nodeEnv: process.env.NODE_ENV,
          hasRazorpayKeyId: !!process.env.RAZORPAY_KEY_ID,
          hasRazorpaySecret: !!process.env.RAZORPAY_SECRET,
          port: PORT
        }
      }));
    } else {
      console.log(`❌ API route not found: ${method} ${path}`);
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        error: 'API endpoint not found',
        path: path,
        method: method
      }));
    }
  } catch (error) {
    console.error('❌ Unhandled error:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      error: 'Internal server error',
      debug: error.message
    }));
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Local API server running on http://localhost:${PORT}`);
  console.log(`📊 Environment check:`);
  console.log(`   - NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
  console.log(`   - RAZORPAY_KEY_ID: ${process.env.RAZORPAY_KEY_ID ? '✅ Set' : '❌ Missing'}`);
  console.log(`   - RAZORPAY_SECRET: ${process.env.RAZORPAY_SECRET ? '✅ Set' : '❌ Missing'}`);
  console.log(`   - VITE_RAZORPAY_KEY_ID: ${process.env.VITE_RAZORPAY_KEY_ID ? '✅ Set' : '❌ Missing'}`);
  console.log(`🔗 API endpoints:`);
  console.log(`   - POST http://localhost:${PORT}/api/razorpay/create-order`);
  console.log(`   - POST http://localhost:${PORT}/api/razorpay/verify-payment`);
  console.log(`   - GET  http://localhost:${PORT}/api/health`);
});

module.exports = server;
