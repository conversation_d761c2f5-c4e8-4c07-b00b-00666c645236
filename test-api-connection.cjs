const http = require('http');

// Test API connection
function testAPI() {
  console.log('🔍 Testing API Connection...\n');

  // Test health endpoint
  const healthReq = http.request({
    hostname: 'localhost',
    port: 3001,
    path: '/health',
    method: 'GET'
  }, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      console.log('✅ Health Check:', JSON.parse(data).message);
      testRazorpayCredentials();
    });
  });

  healthReq.on('error', (err) => {
    console.error('❌ Health check failed:', err.message);
  });

  healthReq.end();
}

// Test Razorpay credentials
function testRazorpayCredentials() {
  const credReq = http.request({
    hostname: 'localhost',
    port: 3001,
    path: '/api/test/razorpay',
    method: 'GET'
  }, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      const result = JSON.parse(data);
      console.log('🔐 Credentials Check:');
      console.log(`   Razorpay Key: ${result.credentials.razorpay_key_id}`);
      console.log(`   Razorpay Secret: ${result.credentials.razorpay_secret}`);
      console.log(`   Supabase URL: ${result.credentials.supabase_url}`);
      console.log(`   Supabase Key: ${result.credentials.supabase_anon_key}`);
      console.log(`   Ready: ${result.ready ? '✅ YES' : '❌ NO'}`);
      
      if (result.ready) {
        testCreateOrder();
      }
    });
  });

  credReq.on('error', (err) => {
    console.error('❌ Credentials check failed:', err.message);
  });

  credReq.end();
}

// Test create order endpoint
function testCreateOrder() {
  console.log('\n💰 Testing Create Order...');
  
  const orderData = JSON.stringify({
    amount: 100, // ₹1.00
    currency: 'INR',
    receipt: 'test_receipt_' + Date.now(),
    notes: {
      test: 'true',
      purpose: 'api_connection_test'
    }
  });

  const orderReq = http.request({
    hostname: 'localhost',
    port: 3001,
    path: '/api/razorpay/create-order',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(orderData)
    }
  }, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        if (result.success) {
          console.log('✅ Order Created Successfully!');
          console.log(`   Order ID: ${result.data.id}`);
          console.log(`   Amount: ₹${result.data.amount / 100}`);
          console.log(`   Status: ${result.data.status}`);
          console.log('\n🎉 API Connection Test: PASSED');
          console.log('\n📱 Ready for Mobile Testing!');
          console.log('🚀 Next Steps:');
          console.log('   1. Keep both servers running');
          console.log('   2. Access from mobile: http://*************:8080');
          console.log('   3. Test the complete payment flow');
        } else {
          console.log('❌ Order Creation Failed:', result.error);
        }
      } catch (err) {
        console.log('❌ Invalid response:', data);
      }
    });
  });

  orderReq.on('error', (err) => {
    console.error('❌ Create order failed:', err.message);
  });

  orderReq.write(orderData);
  orderReq.end();
}

// Start test
testAPI();
