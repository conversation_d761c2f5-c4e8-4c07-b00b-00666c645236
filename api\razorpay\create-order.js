const Razorpay = require('razorpay');

module.exports = async function handler(req, res) {
  // Set CORS headers
  const origin = req.headers.origin;

  if (origin && (
    origin.includes('thebadhees.com') ||
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('vercel.app')
  )) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token, Origin');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    return res.end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    res.writeHead(405, { 'Content-Type': 'application/json' });
    return res.end(JSON.stringify({ success: false, error: 'Method not allowed' }));
  }

  try {
    // Enhanced environment variable validation with detailed logging
    const razorpayKeyId = process.env.RAZORPAY_KEY_ID || process.env.VITE_RAZORPAY_KEY_ID;
    const razorpaySecret = process.env.RAZORPAY_SECRET;

    console.log('🔍 Environment check:', {
      hasKeyId: !!razorpayKeyId,
      hasSecret: !!razorpaySecret,
      keyIdPrefix: razorpayKeyId ? razorpayKeyId.substring(0, 8) + '...' : 'undefined',
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV
    });

    if (!razorpayKeyId || !razorpaySecret) {
      console.error('❌ Missing Razorpay environment variables');
      console.error('Available env vars:', Object.keys(process.env).filter(key =>
        key.includes('RAZORPAY') || key.includes('VITE_RAZORPAY')
      ));
      res.writeHead(500, { 'Content-Type': 'application/json' });
      return res.end(JSON.stringify({
        success: false,
        error: 'Server configuration error: Missing Razorpay credentials',
        debug: {
          hasKeyId: !!razorpayKeyId,
          hasSecret: !!razorpaySecret,
          env: process.env.NODE_ENV
        }
      }));
    }

    // Initialize Razorpay with enhanced error handling
    let razorpay;
    try {
      razorpay = new Razorpay({
        key_id: razorpayKeyId,
        key_secret: razorpaySecret,
      });
      console.log('✅ Razorpay instance created successfully');
    } catch (initError) {
      console.error('❌ Failed to initialize Razorpay:', initError);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      return res.end(JSON.stringify({
        success: false,
        error: 'Failed to initialize payment gateway',
        debug: initError.message
      }));
    }

    // Extract and validate order details from request
    const { amount, currency = 'INR', receipt, notes = {} } = req.body;

    console.log('📝 Order details:', {
      amount,
      currency,
      receipt,
      notesKeys: Object.keys(notes)
    });

    if (!amount || !receipt) {
      console.error('❌ Missing required fields:', { amount: !!amount, receipt: !!receipt });
      res.writeHead(400, { 'Content-Type': 'application/json' });
      return res.end(JSON.stringify({
        success: false,
        error: 'Amount and receipt are required'
      }));
    }

    // Validate amount
    if (isNaN(amount) || amount <= 0) {
      console.error('❌ Invalid amount:', amount);
      res.writeHead(400, { 'Content-Type': 'application/json' });
      return res.end(JSON.stringify({
        success: false,
        error: 'Invalid amount provided'
      }));
    }

    // Create Razorpay order
    const options = {
      amount: Math.round(amount * 100), // Convert to paise
      currency,
      receipt,
      notes,
    };

    console.log('🚀 Creating Razorpay order with options:', options);

    const order = await razorpay.orders.create(options);

    console.log('✅ Razorpay order created successfully:', {
      id: order.id,
      amount: order.amount,
      currency: order.currency
    });

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: order
    }));

  } catch (error) {
    console.error('❌ Error creating Razorpay order:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      error: error.message || 'Failed to create Razorpay order',
      debug: {
        errorType: error.name,
        timestamp: new Date().toISOString()
      }
    }));
  }
};
