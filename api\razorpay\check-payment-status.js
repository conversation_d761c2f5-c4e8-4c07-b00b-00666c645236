const Razorpay = require('razorpay');
const { createClient } = require('@supabase/supabase-js');

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || process.env.VITE_RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_SECRET,
});

// Initialize Supabase
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

module.exports = async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { razorpay_order_id, order_id } = req.body;

    if (!razorpay_order_id) {
      return res.status(400).json({
        success: false,
        error: 'Razorpay order ID is required'
      });
    }

    console.log('🔍 Checking payment status for order:', razorpay_order_id);

    // Fetch order details from Razorpay
    const order = await razorpay.orders.fetch(razorpay_order_id);
    console.log('📋 Razorpay order details:', {
      id: order.id,
      status: order.status,
      amount: order.amount,
      amount_paid: order.amount_paid,
      amount_due: order.amount_due
    });

    // Check if order has any payments
    const payments = await razorpay.orders.fetchPayments(razorpay_order_id);
    console.log('💳 Found payments:', payments.items.length);

    let paymentStatus = 'pending';
    let paymentId = null;

    if (payments.items.length > 0) {
      // Get the latest payment
      const latestPayment = payments.items[0];
      paymentStatus = latestPayment.status;
      paymentId = latestPayment.id;
      
      console.log('💰 Latest payment:', {
        id: latestPayment.id,
        status: latestPayment.status,
        method: latestPayment.method,
        amount: latestPayment.amount
      });

      // If payment is captured, update our database
      if (paymentStatus === 'captured' || paymentStatus === 'authorized') {
        console.log('✅ Payment successful, updating database...');
        
        // Update payment record in Supabase
        const { error: paymentError } = await supabase
          .from('razorpay_payments')
          .update({
            razorpay_payment_id: paymentId,
            status: paymentStatus,
            method: latestPayment.method || 'online',
            updated_at: new Date().toISOString()
          })
          .eq('razorpay_order_id', razorpay_order_id);

        if (paymentError) {
          console.error('❌ Error updating payment record:', paymentError);
        } else {
          console.log('✅ Payment record updated successfully');
        }

        // Update order if order_id is provided and it's not a temp order
        if (order_id && !order_id.startsWith('temp_')) {
          console.log('📦 Updating order with payment details:', order_id);
          const { error: orderError } = await supabase
            .from('orders')
            .update({
              razorpay_payment_id: paymentId,
              payment_status: 'paid',
              status: 'paid',
              updated_at: new Date().toISOString()
            })
            .eq('id', order_id);

          if (orderError) {
            console.error('❌ Error updating order:', orderError);
          } else {
            console.log('✅ Order updated successfully');
          }
        }
      }
    }

    // Return payment status
    return res.status(200).json({
      success: true,
      payment_status: paymentStatus,
      payment_id: paymentId,
      order_status: order.status,
      amount_paid: order.amount_paid,
      amount_due: order.amount_due,
      payments_count: payments.items.length
    });

  } catch (error) {
    console.error('❌ Error checking payment status:', error);
    
    // Handle specific Razorpay errors
    if (error.statusCode === 400) {
      return res.status(400).json({
        success: false,
        error: 'Invalid order ID or order not found'
      });
    }
    
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to check payment status'
    });
  }
};
